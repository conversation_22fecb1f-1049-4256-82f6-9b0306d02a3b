"""
Debug script for BlenderGPT runtime errors
Helps identify the exact source of the bpy.ops.gpt4.send_message() error
"""

import sys
import os
import traceback

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_property_access():
    """Test if properties can be accessed without errors"""
    print("🔍 Testing Property Access...")
    
    try:
        # Test properties module
        from properties import GPT4_ChatMessage, register_properties, unregister_properties
        print("  ✅ Properties module imported successfully")
        
        # Test PropertyGroup class structure
        if hasattr(GPT4_ChatMessage, '__annotations__'):
            annotations = GPT4_ChatMessage.__annotations__
            print(f"  ✅ GPT4_ChatMessage has {len(annotations)} properties")
            for prop_name, prop_type in annotations.items():
                print(f"    - {prop_name}: {prop_type}")
        else:
            print("  ❌ GPT4_ChatMessage has no annotations")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ Property access test failed: {e}")
        print(f"  📋 Traceback: {traceback.format_exc()}")
        return False

def test_utilities_functions():
    """Test utilities functions that might cause the error"""
    print("\n🛠️ Testing Utilities Functions...")
    
    try:
        from utilities import (
            generate_blender_code, execute_code_safely,
            get_api_key, enhance_prompt_with_context
        )
        print("  ✅ Utilities functions imported successfully")
        
        # Test function signatures
        import inspect
        
        # Test generate_blender_code signature
        sig = inspect.signature(generate_blender_code)
        print(f"  📋 generate_blender_code signature: {sig}")
        
        # Test execute_code_safely signature
        sig = inspect.signature(execute_code_safely)
        print(f"  📋 execute_code_safely signature: {sig}")
        
        # Test enhance_prompt_with_context signature
        sig = inspect.signature(enhance_prompt_with_context)
        print(f"  📋 enhance_prompt_with_context signature: {sig}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Utilities test failed: {e}")
        print(f"  📋 Traceback: {traceback.format_exc()}")
        return False

def test_operator_structure():
    """Test the operator class structure"""
    print("\n🎯 Testing Operator Structure...")
    
    try:
        # Import the main module
        import __init__ as main_module
        
        # Check if GPT4_OT_Execute exists
        if hasattr(main_module, 'GPT4_OT_Execute'):
            operator_class = main_module.GPT4_OT_Execute
            print("  ✅ GPT4_OT_Execute class found")
            
            # Check required attributes
            required_attrs = ['bl_idname', 'bl_label', 'bl_options', 'execute']
            for attr in required_attrs:
                if hasattr(operator_class, attr):
                    print(f"    ✅ {attr}: {getattr(operator_class, attr)}")
                else:
                    print(f"    ❌ Missing {attr}")
                    return False
            
            # Check execute method signature
            import inspect
            sig = inspect.signature(operator_class.execute)
            print(f"  📋 execute method signature: {sig}")
            
        else:
            print("  ❌ GPT4_OT_Execute class not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ Operator structure test failed: {e}")
        print(f"  📋 Traceback: {traceback.format_exc()}")
        return False

def test_import_chain():
    """Test the complete import chain"""
    print("\n🔗 Testing Import Chain...")
    
    try:
        # Test each import step
        print("  🔄 Testing step 1: properties")
        from properties import GPT4_ChatMessage
        print("    ✅ Properties imported")
        
        print("  🔄 Testing step 2: utilities")
        from utilities import generate_blender_code
        print("    ✅ Utilities imported")
        
        print("  🔄 Testing step 3: security")
        from security import CodeSecurityValidator
        print("    ✅ Security imported")
        
        print("  🔄 Testing step 4: api_providers")
        from api_providers import APIProviderManager
        print("    ✅ API providers imported")
        
        print("  🔄 Testing step 5: main module")
        import __init__ as main_module
        print("    ✅ Main module imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import chain test failed: {e}")
        print(f"  📋 Traceback: {traceback.format_exc()}")
        return False

def analyze_error_context():
    """Analyze the specific error context from the traceback"""
    print("\n🔬 Analyzing Error Context...")
    
    # The error occurs in:
    # File "C:\Program Files\Blender Foundation\Blender\4.4\scripts\modules\bpy\ops.py", line 109, in __call__
    # ret = _op_call(self.idname_py(), kw)
    
    print("  📋 Error Analysis:")
    print("    - Error occurs in bpy.ops.__call__ method")
    print("    - This suggests the operator is registered but fails during execution")
    print("    - The error 'Failed to generate code. Please try again.' suggests")
    print("      the issue is in the generate_blender_code() function")
    
    print("\n  🎯 Likely causes:")
    print("    1. API key not properly configured")
    print("    2. Network connectivity issues")
    print("    3. API provider initialization failure")
    print("    4. Invalid model selection")
    print("    5. Missing dependencies")
    
    print("\n  💡 Debugging steps:")
    print("    1. Check API key configuration")
    print("    2. Test network connectivity")
    print("    3. Verify API provider settings")
    print("    4. Test with minimal input")

def main():
    """Run all debug tests"""
    print("🐛 BlenderGPT Debug Test Suite")
    print("=" * 60)
    
    tests = [
        ("Property Access", test_property_access),
        ("Utilities Functions", test_utilities_functions),
        ("Operator Structure", test_operator_structure),
        ("Import Chain", test_import_chain),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    # Always run error analysis
    analyze_error_context()
    
    print("\n" + "=" * 60)
    print(f"📊 Debug Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All structural tests passed. The error is likely runtime-related.")
        print("💡 Check API configuration and network connectivity.")
    else:
        print(f"⚠️ {total - passed} structural issue(s) found.")
        print("🔧 Fix these issues before testing in Blender.")

if __name__ == "__main__":
    main()
