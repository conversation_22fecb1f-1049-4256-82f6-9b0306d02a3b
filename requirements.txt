# Core HTTP and async libraries
requests>=2.31.0
aiohttp>=3.9.0
urllib3>=2.0.0

# AI Provider APIs
openai<1.0.0  # Using 0.28.x for compatibility
# anthropic>=0.25.0  # Commented out due to Rust dependency issues

# Security and validation
certifi>=2023.7.22
charset-normalizer>=3.2.0

# Utility libraries
tqdm>=4.66.0
typing-extensions>=4.7.0

# Optional: For enhanced functionality
# pydantic>=2.0.0  # Commented out due to Rust dependency
# httpx>=0.25.0    # Alternative HTTP client

# Development and testing (optional)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0

# Notes:
# - OpenRouter uses OpenAI-compatible API, no additional library needed
# - Anthropic support implemented with direct HTTP requests
# - Some packages commented out due to Rust compilation requirements