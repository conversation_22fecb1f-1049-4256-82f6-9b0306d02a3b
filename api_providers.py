"""
BlenderGPT API Providers Module
Supports multiple LLM providers with OpenAI-compatible APIs
Enhanced with modern API support and comprehensive error handling
"""

import json
import requests
import asyncio
import aiohttp
import traceback
import time
from typing import Dict, List, Optional, Iterator, Any, Tuple, Union
from abc import ABC, abstractmethod
from enum import Enum

class APIProvider(Enum):
    """Supported API providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    CUSTOM = "custom"

class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, api_key: str, base_url: str, model: str):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = 30
        
    @abstractmethod
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create a chat completion"""
        pass
    
    @abstractmethod
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create a streaming chat completion"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        pass

class OpenAIProvider(LLMProvider):
    """Enhanced OpenAI API provider with modern API support"""

    def __init__(self, api_key: str, model: str = "gpt-4", base_url: str = None):
        base_url = base_url or "https://api.openai.com/v1"
        super().__init__(api_key, base_url, model)
        self.max_retries = 3
        self.retry_delay = 1

    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create OpenAI chat completion with enhanced error handling"""
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "BlenderGPT/2.1.0"
        }

        # Prepare data with validation
        data = {
            "model": self.model,
            "messages": self._validate_messages(messages),
            "max_tokens": kwargs.get("max_tokens", 1500),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 1.0),
            "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
            "presence_penalty": kwargs.get("presence_penalty", 0.0),
        }

        # Add any additional kwargs
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make request with retry logic
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limit
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2 ** attempt)
                        print(f"Rate limited, waiting {wait_time} seconds...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f"OpenAI API rate limit exceeded after {self.max_retries} attempts")
                elif response.status_code == 401:
                    raise Exception("OpenAI API authentication failed - check your API key")
                elif response.status_code == 403:
                    raise Exception("OpenAI API access forbidden - check your permissions")
                else:
                    raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")

            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    print(f"Request timeout, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("OpenAI API request timed out after multiple attempts")
            except requests.exceptions.ConnectionError:
                if attempt < self.max_retries - 1:
                    print(f"Connection error, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("Failed to connect to OpenAI API after multiple attempts")
            except Exception as e:
                if attempt < self.max_retries - 1:
                    print(f"Unexpected error, retrying... (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception(f"OpenAI API request failed: {e}")

        raise Exception("OpenAI API request failed after all retry attempts")
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create OpenAI streaming chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout,
            stream=True
        )
        
        if response.status_code != 200:
            raise Exception(f"OpenAI API error: {response.status_code}")
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue
    
    def get_available_models(self) -> List[str]:
        """Get OpenAI available models with API call"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "User-Agent": "BlenderGPT/2.1.0"
            }

            response = requests.get(
                f"{self.base_url}/models",
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                models_data = response.json()
                # Filter for chat models
                chat_models = []
                for model in models_data.get("data", []):
                    model_id = model.get("id", "")
                    if any(prefix in model_id for prefix in ["gpt-4", "gpt-3.5"]):
                        chat_models.append(model_id)

                if chat_models:
                    return sorted(chat_models)

        except Exception as e:
            print(f"Failed to fetch OpenAI models: {e}")

        # Fallback to known models
        return [
            "gpt-4-turbo-preview",
            "gpt-4-1106-preview",
            "gpt-4",
            "gpt-3.5-turbo-1106",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]

    def _validate_messages(self, messages: List[Dict]) -> List[Dict]:
        """Validate and clean message format"""
        validated_messages = []

        for msg in messages:
            if not isinstance(msg, dict):
                continue

            role = msg.get("role", "").lower()
            content = msg.get("content", "")

            if role not in ["system", "user", "assistant"]:
                continue

            if not content or not isinstance(content, str):
                continue

            # Truncate very long messages
            if len(content) > 8000:
                content = content[:8000] + "... [truncated]"

            validated_messages.append({
                "role": role,
                "content": content
            })

        return validated_messages

class AnthropicProvider(LLMProvider):
    """Anthropic Claude API provider with enhanced error handling"""

    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229"):
        super().__init__(api_key, "https://api.anthropic.com/v1", model)
        self.max_retries = 3
        self.retry_delay = 1

        print(f"AnthropicProvider initialized with model: {self.model}")

    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create Anthropic chat completion with enhanced error handling"""
        if not self.api_key:
            raise Exception("Anthropic API key is required")

        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01",
            "User-Agent": "BlenderGPT/2.1.0"
        }

        # Convert OpenAI format to Anthropic format
        try:
            anthropic_messages = self._convert_messages_to_anthropic(messages)
        except Exception as e:
            raise Exception(f"Failed to convert messages for Anthropic: {e}")

        data = {
            "model": self.model,
            "messages": anthropic_messages,
            "max_tokens": kwargs.get("max_tokens", 1500),
            "temperature": kwargs.get("temperature", 0.7),
        }

        # Add any additional kwargs that Anthropic supports
        for key, value in kwargs.items():
            if key not in data and key in ["top_p", "top_k", "stop_sequences"]:
                data[key] = value

        # Make request with retry logic
        for attempt in range(self.max_retries):
            try:
                print(f"Attempting Anthropic API call (attempt {attempt + 1}): {self.model}")

                response = requests.post(
                    f"{self.base_url}/messages",
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )

                print(f"Anthropic API response status: {response.status_code}")

                if response.status_code == 200:
                    anthropic_response = response.json()
                    result = self._convert_anthropic_response(anthropic_response)
                    print(f"Anthropic API success: {len(str(result))} chars response")
                    return result
                elif response.status_code == 429:  # Rate limit
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2 ** attempt)
                        print(f"Anthropic rate limited, waiting {wait_time} seconds...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f"Anthropic API rate limit exceeded after {self.max_retries} attempts")
                elif response.status_code == 401:
                    raise Exception("Anthropic API authentication failed - check your API key")
                elif response.status_code == 400:
                    error_text = response.text[:500] if response.text else "Bad request"
                    raise Exception(f"Anthropic API bad request: {error_text}")
                else:
                    error_text = response.text[:500] if response.text else "No error details"
                    raise Exception(f"Anthropic API error {response.status_code}: {error_text}")

            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    print(f"Anthropic request timeout, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("Anthropic API request timed out after multiple attempts")
            except requests.exceptions.ConnectionError:
                if attempt < self.max_retries - 1:
                    print(f"Anthropic connection error, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("Failed to connect to Anthropic API")
            except Exception as e:
                if attempt < self.max_retries - 1:
                    print(f"Anthropic unexpected error, retrying... (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception(f"Anthropic API request failed: {e}")

        raise Exception("Anthropic API request failed after all retry attempts")
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create Anthropic streaming chat completion"""
        # Anthropic streaming implementation
        # For now, fall back to non-streaming
        response = self.create_chat_completion(messages, **kwargs)
        yield response
    
    def get_available_models(self) -> List[str]:
        """Get Anthropic available models"""
        return ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]
    
    def _convert_messages_to_anthropic(self, messages: List[Dict]) -> List[Dict]:
        """Convert OpenAI message format to Anthropic format"""
        anthropic_messages = []
        for msg in messages:
            if msg["role"] == "system":
                # Anthropic handles system messages differently
                continue
            anthropic_messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        return anthropic_messages
    
    def _convert_anthropic_response(self, response: Dict) -> Dict:
        """Convert Anthropic response to OpenAI format"""
        return {
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": response["content"][0]["text"]
                },
                "finish_reason": "stop"
            }],
            "usage": response.get("usage", {})
        }

class OllamaProvider(LLMProvider):
    """Ollama local API provider"""
    
    def __init__(self, api_key: str = "", base_url: str = "http://localhost:11434", model: str = "llama2"):
        super().__init__(api_key, base_url, model)
    
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create Ollama chat completion"""
        data = {
            "model": self.model,
            "messages": messages,
            "stream": False,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/api/chat",
            json=data,
            timeout=self.timeout
        )
        
        if response.status_code != 200:
            raise Exception(f"Ollama API error: {response.status_code} - {response.text}")
        
        ollama_response = response.json()
        return self._convert_ollama_response(ollama_response)
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create Ollama streaming chat completion"""
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/api/chat",
            json=data,
            timeout=self.timeout,
            stream=True
        )
        
        if response.status_code != 200:
            raise Exception(f"Ollama API error: {response.status_code}")
        
        for line in response.iter_lines():
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    yield self._convert_ollama_stream_response(data)
                except json.JSONDecodeError:
                    continue
    
    def get_available_models(self) -> List[str]:
        """Get Ollama available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                return [model["name"] for model in models]
        except:
            pass
        return ["llama2", "codellama", "mistral"]
    
    def _convert_ollama_response(self, response: Dict) -> Dict:
        """Convert Ollama response to OpenAI format"""
        return {
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": response.get("message", {}).get("content", "")
                },
                "finish_reason": "stop"
            }]
        }
    
    def _convert_ollama_stream_response(self, response: Dict) -> Dict:
        """Convert Ollama stream response to OpenAI format"""
        return {
            "choices": [{
                "delta": {
                    "content": response.get("message", {}).get("content", "")
                },
                "finish_reason": "stop" if response.get("done", False) else None
            }]
        }

class OpenRouterProvider(LLMProvider):
    """OpenRouter API provider - Access to hundreds of AI models through unified API"""

    def __init__(self, api_key: str, model: str = "openai/gpt-3.5-turbo"):
        super().__init__(api_key, "https://openrouter.ai/api/v1", model)
        self.max_retries = 3
        self.retry_delay = 1

        print(f"OpenRouterProvider initialized with model: {self.model}")

    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create OpenRouter chat completion with enhanced error handling"""
        if not self.api_key:
            raise Exception("OpenRouter API key is required")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "BlenderGPT/2.1.0",
            "HTTP-Referer": "https://blender.org",  # Optional: for OpenRouter rankings
            "X-Title": "BlenderGPT"  # Optional: for OpenRouter rankings
        }

        # Prepare data with validation
        data = {
            "model": self.model,
            "messages": self._validate_messages(messages),
            "max_tokens": kwargs.get("max_tokens", 1500),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 1.0),
        }

        # Add any additional kwargs
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make request with retry logic
        for attempt in range(self.max_retries):
            try:
                print(f"Attempting OpenRouter API call (attempt {attempt + 1}): {self.model}")

                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )

                print(f"OpenRouter API response status: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    print(f"OpenRouter API success: {len(str(result))} chars response")
                    return result
                elif response.status_code == 429:  # Rate limit
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2 ** attempt)
                        print(f"OpenRouter rate limited, waiting {wait_time} seconds...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f"OpenRouter API rate limit exceeded after {self.max_retries} attempts")
                elif response.status_code == 401:
                    raise Exception("OpenRouter API authentication failed - check your API key")
                elif response.status_code == 402:
                    raise Exception("OpenRouter API payment required - check your credits")
                elif response.status_code == 404:
                    raise Exception(f"OpenRouter model not found: {self.model}")
                elif response.status_code == 422:
                    raise Exception(f"OpenRouter API validation error - check model name: {self.model}")
                else:
                    error_text = response.text[:500] if response.text else "No error details"
                    raise Exception(f"OpenRouter API error {response.status_code}: {error_text}")

            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    print(f"OpenRouter request timeout, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("OpenRouter API request timed out after multiple attempts")
            except requests.exceptions.ConnectionError:
                if attempt < self.max_retries - 1:
                    print(f"OpenRouter connection error, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("Failed to connect to OpenRouter API")
            except Exception as e:
                if attempt < self.max_retries - 1:
                    print(f"OpenRouter unexpected error, retrying... (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception(f"OpenRouter API request failed: {e}")

        raise Exception("OpenRouter API request failed after all retry attempts")

    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create OpenRouter streaming chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://blender.org",
            "X-Title": "BlenderGPT"
        }

        data = {
            "model": self.model,
            "messages": self._validate_messages(messages),
            "stream": True,
            **kwargs
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout,
            stream=True
        )

        if response.status_code != 200:
            raise Exception(f"OpenRouter streaming API error: {response.status_code} - {response.text}")

        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue

    def get_available_models(self) -> List[str]:
        """Get OpenRouter available models"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "User-Agent": "BlenderGPT/2.1.0"
            }

            print("Fetching models from OpenRouter...")

            response = requests.get(
                f"{self.base_url}/models",
                headers=headers,
                timeout=15
            )

            print(f"OpenRouter models API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                models = []

                for model in data.get("data", []):
                    model_id = model.get("id", "")
                    if model_id:
                        models.append(model_id)

                if models:
                    print(f"Found {len(models)} models from OpenRouter")
                    # Sort models by popularity/provider
                    sorted_models = sorted(models, key=lambda x: (
                        0 if x.startswith("openai/") else
                        1 if x.startswith("anthropic/") else
                        2 if x.startswith("google/") else
                        3 if x.startswith("meta-llama/") else
                        4 if x.startswith("mistralai/") else
                        5
                    ))
                    return sorted_models
                else:
                    print("No models found in OpenRouter response")

            elif response.status_code == 401:
                print("OpenRouter authentication failed for models endpoint")
            else:
                print(f"OpenRouter models API error: {response.status_code} - {response.text[:200]}")

        except requests.exceptions.Timeout:
            print("Timeout while fetching models from OpenRouter")
        except requests.exceptions.ConnectionError:
            print("Connection error while fetching models from OpenRouter")
        except Exception as e:
            print(f"Error fetching models from OpenRouter: {e}")

        # Fallback to popular models
        fallback_models = [
            "openai/gpt-4-turbo",
            "openai/gpt-4",
            "openai/gpt-3.5-turbo",
            "anthropic/claude-3-sonnet",
            "anthropic/claude-3-haiku",
            "google/gemini-pro",
            "meta-llama/llama-3.1-8b-instruct:free",
            "mistralai/mistral-7b-instruct:free",
            "microsoft/wizardlm-2-8x22b",
            "cohere/command-r-plus"
        ]
        print(f"Using OpenRouter fallback models: {len(fallback_models)} models")
        return fallback_models

    def _validate_messages(self, messages: List[Dict]) -> List[Dict]:
        """Validate and clean message format for OpenRouter"""
        validated_messages = []

        for msg in messages:
            if not isinstance(msg, dict):
                continue

            role = msg.get("role", "").lower()
            content = msg.get("content", "")

            if role not in ["system", "user", "assistant"]:
                continue

            if not content or not isinstance(content, str):
                continue

            # Truncate very long messages
            if len(content) > 8000:
                content = content[:8000] + "... [truncated]"

            validated_messages.append({
                "role": role,
                "content": content
            })

        return validated_messages

class CustomProvider(LLMProvider):
    """Enhanced Custom OpenAI-compatible API provider with better error handling"""

    def __init__(self, api_key: str, base_url: str, model: str):
        super().__init__(api_key, base_url, model)
        self.max_retries = 3
        self.retry_delay = 1

        # Clean up base URL
        if base_url and not base_url.startswith(('http://', 'https://')):
            base_url = f"https://{base_url}"
        self.base_url = base_url.rstrip('/') if base_url else ""

        print(f"CustomProvider initialized: {self.base_url}, model: {self.model}")

    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create custom API chat completion with enhanced error handling"""
        if not self.base_url:
            raise Exception("Custom API base URL is required")

        if not self.api_key:
            raise Exception("Custom API key is required")

        # Prepare headers - some APIs might not need Authorization
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "BlenderGPT/2.1.0"
        }

        if self.api_key and self.api_key.strip():
            headers["Authorization"] = f"Bearer {self.api_key}"

        # Prepare data with validation
        data = {
            "model": self.model,
            "messages": self._validate_messages(messages),
            "max_tokens": kwargs.get("max_tokens", 1500),
            "temperature": kwargs.get("temperature", 0.7),
        }

        # Add any additional kwargs
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make request with retry logic
        for attempt in range(self.max_retries):
            try:
                print(f"Attempting Custom API call (attempt {attempt + 1}): {self.base_url}/chat/completions")

                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )

                print(f"Custom API response status: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    print(f"Custom API success: {len(str(result))} chars response")
                    return result
                elif response.status_code == 429:  # Rate limit
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2 ** attempt)
                        print(f"Rate limited, waiting {wait_time} seconds...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f"Custom API rate limit exceeded after {self.max_retries} attempts")
                elif response.status_code == 401:
                    raise Exception("Custom API authentication failed - check your API key")
                elif response.status_code == 404:
                    raise Exception(f"Custom API endpoint not found: {self.base_url}/chat/completions")
                elif response.status_code == 422:
                    raise Exception(f"Custom API validation error - check model name: {self.model}")
                else:
                    error_text = response.text[:500] if response.text else "No error details"
                    raise Exception(f"Custom API error {response.status_code}: {error_text}")

            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    print(f"Request timeout, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception("Custom API request timed out after multiple attempts")
            except requests.exceptions.ConnectionError:
                if attempt < self.max_retries - 1:
                    print(f"Connection error, retrying... (attempt {attempt + 1})")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception(f"Failed to connect to Custom API: {self.base_url}")
            except Exception as e:
                if attempt < self.max_retries - 1:
                    print(f"Unexpected error, retrying... (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception(f"Custom API request failed: {e}")

        raise Exception("Custom API request failed after all retry attempts")

    def _validate_messages(self, messages: List[Dict]) -> List[Dict]:
        """Validate and clean message format for custom APIs"""
        validated_messages = []

        for msg in messages:
            if not isinstance(msg, dict):
                continue

            role = msg.get("role", "").lower()
            content = msg.get("content", "")

            if role not in ["system", "user", "assistant"]:
                continue

            if not content or not isinstance(content, str):
                continue

            # Truncate very long messages
            if len(content) > 8000:
                content = content[:8000] + "... [truncated]"

            validated_messages.append({
                "role": role,
                "content": content
            })

        return validated_messages
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create custom API streaming chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout,
            stream=True
        )
        
        if response.status_code != 200:
            raise Exception(f"Custom API error: {response.status_code}")
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue
    
    def get_available_models(self) -> List[str]:
        """Get custom API available models with enhanced error handling"""
        if not self.base_url:
            print("Warning: No base URL set for custom API")
            return [self.model] if self.model else ["gpt-3.5-turbo"]

        try:
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "BlenderGPT/2.1.0"
            }

            if self.api_key and self.api_key.strip():
                headers["Authorization"] = f"Bearer {self.api_key}"

            print(f"Fetching models from: {self.base_url}/models")

            response = requests.get(
                f"{self.base_url}/models",
                headers=headers,
                timeout=10
            )

            print(f"Models API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()

                # Handle different response formats
                if "data" in data:
                    # OpenAI-style response
                    models = [model["id"] for model in data["data"] if isinstance(model, dict) and "id" in model]
                elif "models" in data:
                    # Alternative format
                    models = data["models"]
                elif isinstance(data, list):
                    # Direct list format
                    models = [model["id"] if isinstance(model, dict) else str(model) for model in data]
                else:
                    print(f"Unexpected models response format: {data}")
                    models = [self.model] if self.model else []

                if models:
                    print(f"Found {len(models)} models from custom API")
                    return models
                else:
                    print("No models found in API response")

            elif response.status_code == 404:
                print("Models endpoint not available on this API")
            elif response.status_code == 401:
                print("Authentication failed for models endpoint")
            else:
                print(f"Models API error: {response.status_code} - {response.text[:200]}")

        except requests.exceptions.Timeout:
            print("Timeout while fetching models from custom API")
        except requests.exceptions.ConnectionError:
            print(f"Connection error while fetching models from: {self.base_url}")
        except Exception as e:
            print(f"Error fetching models from custom API: {e}")

        # Fallback to current model or default
        fallback_models = [self.model] if self.model else ["gpt-3.5-turbo", "gpt-4"]
        print(f"Using fallback models: {fallback_models}")
        return fallback_models

class APIProviderManager:
    """Manager for different API providers"""
    
    def __init__(self):
        self.providers = {}
    
    def register_provider(self, provider_type: APIProvider, provider: LLMProvider):
        """Register a new provider"""
        self.providers[provider_type] = provider
    
    def get_provider(self, provider_type: APIProvider) -> Optional[LLMProvider]:
        """Get a registered provider"""
        return self.providers.get(provider_type)
    
    def create_provider(self, provider_type: APIProvider, **kwargs) -> LLMProvider:
        """Create a new provider instance"""
        if provider_type == APIProvider.OPENAI:
            return OpenAIProvider(**kwargs)
        elif provider_type == APIProvider.ANTHROPIC:
            return AnthropicProvider(**kwargs)
        elif provider_type == APIProvider.OLLAMA:
            return OllamaProvider(**kwargs)
        elif provider_type == APIProvider.OPENROUTER:
            return OpenRouterProvider(**kwargs)
        elif provider_type == APIProvider.CUSTOM:
            return CustomProvider(**kwargs)
        else:
            raise ValueError(f"Unsupported provider type: {provider_type}")
