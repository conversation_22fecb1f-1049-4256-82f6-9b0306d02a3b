"""
BlenderGPT Properties Module
Defines custom PropertyGroup classes and property initialization
"""

import bpy
from bpy.props import StringProperty, FloatProperty, BoolProperty, EnumProperty, CollectionProperty


class GPT4_ChatMessage(bpy.types.PropertyGroup):
    """PropertyGroup for storing chat messages"""
    
    type: StringProperty(
        name="Type",
        description="Message type (user/assistant)",
        default="user"
    )
    
    content: StringProperty(
        name="Content", 
        description="Message content",
        default=""
    )
    
    timestamp: FloatProperty(
        name="Timestamp",
        description="Message timestamp",
        default=0.0
    )
    
    approved: BoolProperty(
        name="Approved",
        description="Whether the message was approved for execution",
        default=False
    )


def register_properties():
    """Register all custom properties"""
    
    # Register PropertyGroup classes
    bpy.utils.register_class(GPT4_ChatMessage)
    
    # Chat history collection
    bpy.types.Scene.gpt4_chat_history = CollectionProperty(
        type=GPT4_ChatMessage,
        name="Chat History",
        description="BlenderGPT chat history"
    )

    # API Provider selection
    bpy.types.Scene.gpt4_api_provider = EnumProperty(
        name="API Provider",
        description="Select the API provider to use",
        items=[
            ("openai", "OpenAI", "Use OpenAI API"),
            ("anthropic", "Anthropic Claude", "Use Anthropic Claude API"),
            ("ollama", "Ollama (Local)", "Use local Ollama API"),
            ("openrouter", "OpenRouter", "Access hundreds of AI models through OpenRouter"),
            ("custom", "Custom API", "Use custom OpenAI-compatible API"),
        ],
        default="openai",
    )

    # Model selection
    bpy.types.Scene.gpt4_model = StringProperty(
        name="Model",
        description="Model to use",
        default="gpt-4",
    )

    # Custom API settings
    bpy.types.Scene.gpt4_custom_base_url = StringProperty(
        name="Custom Base URL",
        description="Base URL for custom API",
        default="http://localhost:8000/v1",
    )

    # Chat input
    bpy.types.Scene.gpt4_chat_input = StringProperty(
        name="Message",
        description="Enter your message",
        default="",
    )

    # UI state properties
    bpy.types.Scene.gpt4_button_pressed = BoolProperty(
        name="Button Pressed",
        description="Whether the execute button is currently pressed",
        default=False
    )
    
    bpy.types.Scene.gpt4_code_preview_enabled = BoolProperty(
        name="Code Preview",
        description="Show code preview before execution",
        default=True,
    )
    
    bpy.types.Scene.gpt4_security_enabled = BoolProperty(
        name="Security Validation",
        description="Enable security validation for generated code",
        default=True,
    )


def unregister_properties():
    """Unregister all custom properties"""
    
    try:
        # Clear scene properties
        del bpy.types.Scene.gpt4_chat_history
        del bpy.types.Scene.gpt4_api_provider
        del bpy.types.Scene.gpt4_model
        del bpy.types.Scene.gpt4_custom_base_url
        del bpy.types.Scene.gpt4_chat_input
        del bpy.types.Scene.gpt4_button_pressed
        del bpy.types.Scene.gpt4_code_preview_enabled
        del bpy.types.Scene.gpt4_security_enabled
        
    except AttributeError:
        # Properties may not exist if addon failed to initialize
        pass
    
    # Unregister PropertyGroup classes
    try:
        bpy.utils.unregister_class(GPT4_ChatMessage)
    except (ValueError, RuntimeError):
        # Class may not be registered
        pass
