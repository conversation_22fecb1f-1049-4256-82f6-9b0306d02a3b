"""
Test script for BlenderGPT improvements
Tests the enhanced security, error handling, and import systems
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_security_improvements():
    """Test enhanced security validation"""
    print("🔒 Testing Security Improvements...")
    
    try:
        from security import CodeSecurityValidator, create_safe_execution_environment
        
        validator = CodeSecurityValidator()
        
        # Test cases for enhanced security
        test_cases = [
            # Safe code
            {
                "name": "Safe Blender Code",
                "code": "import bpy\nbpy.ops.mesh.primitive_cube_add(location=(0, 0, 0))",
                "should_be_safe": True
            },
            # Dangerous imports
            {
                "name": "Dangerous OS Import",
                "code": "import os\nos.system('rm -rf /')",
                "should_be_safe": False
            },
            # Code length test
            {
                "name": "Very Long Code",
                "code": "x = 1\n" * 5000,  # 5000 lines
                "should_be_safe": False
            },
            # Nesting depth test
            {
                "name": "Deep Nesting",
                "code": "if True:\n" + "    if True:\n" * 15 + "        pass",
                "should_be_safe": True  # Should generate warning but not error
            },
            # Suspicious patterns
            {
                "name": "String Formatting",
                "code": "name = 'test'\nprint('Hello {}'.format(name))",
                "should_be_safe": True  # Should generate warning
            }
        ]
        
        for test_case in test_cases:
            try:
                is_safe, warnings, errors = validator.validate_code(test_case["code"])
                expected = test_case["should_be_safe"]
                
                status = "✅" if (is_safe == expected) else "❌"
                print(f"  {status} {test_case['name']}: safe={is_safe} (expected={expected})")
                
                if warnings:
                    print(f"    Warnings: {len(warnings)}")
                if errors:
                    print(f"    Errors: {len(errors)}")
                    
            except Exception as e:
                print(f"  ❌ {test_case['name']}: Test failed with error: {e}")
        
        # Test safe execution environment
        try:
            safe_env = create_safe_execution_environment()
            print("  ✅ Safe execution environment created successfully")
            print(f"    Available modules: {list(safe_env.keys())}")
        except Exception as e:
            print(f"  ❌ Safe execution environment failed: {e}")
            
    except ImportError as e:
        print(f"  ❌ Failed to import security module: {e}")

def test_api_providers():
    """Test enhanced API providers"""
    print("\n🌐 Testing API Provider Improvements...")
    
    try:
        from api_providers import APIProviderManager, APIProvider, OpenAIProvider
        
        # Test provider creation
        manager = APIProviderManager()
        
        # Test OpenAI provider with enhanced features
        try:
            provider = OpenAIProvider(api_key="test_key", model="gpt-4")
            print("  ✅ OpenAI provider created successfully")
            
            # Test model validation
            models = provider.get_available_models()
            print(f"  ✅ Available models: {len(models)} models")
            
        except Exception as e:
            print(f"  ❌ OpenAI provider test failed: {e}")
            
        # Test provider manager
        try:
            test_provider = manager.create_provider(
                APIProvider.OPENAI, 
                api_key="test", 
                model="gpt-4"
            )
            print("  ✅ Provider manager working correctly")
        except Exception as e:
            print(f"  ❌ Provider manager test failed: {e}")
            
    except ImportError as e:
        print(f"  ❌ Failed to import API providers: {e}")

def test_properties():
    """Test property system"""
    print("\n📋 Testing Property System...")
    
    try:
        from properties import GPT4_ChatMessage, register_properties, unregister_properties
        
        print("  ✅ Properties module imported successfully")
        print(f"  ✅ GPT4_ChatMessage class available: {GPT4_ChatMessage}")
        
        # Test property registration functions
        print("  ✅ Property registration functions available")
        
    except ImportError as e:
        print(f"  ❌ Failed to import properties module: {e}")

def test_utilities():
    """Test utilities improvements"""
    print("\n🛠️ Testing Utilities Improvements...")
    
    try:
        from utilities import safe_import_modules
        
        # Test safe import system
        print("  ✅ Safe import system available")
        
        # Test other utility functions
        utility_functions = [
            'generate_blender_code', 'execute_code_safely', 
            'get_api_key', 'enhance_prompt_with_context'
        ]
        
        for func_name in utility_functions:
            try:
                from utilities import *
                if func_name in globals():
                    print(f"  ✅ {func_name} function available")
                else:
                    print(f"  ⚠️ {func_name} function not found")
            except Exception as e:
                print(f"  ❌ Error checking {func_name}: {e}")
                
    except ImportError as e:
        print(f"  ❌ Failed to import utilities: {e}")

def main():
    """Run all improvement tests"""
    print("🚀 BlenderGPT Improvements Test Suite")
    print("=" * 60)
    
    test_security_improvements()
    test_api_providers()
    test_properties()
    test_utilities()
    
    print("\n" + "=" * 60)
    print("✅ Improvement tests completed!")
    print("\n💡 Summary:")
    print("- Enhanced security validation with comprehensive threat detection")
    print("- Improved API providers with retry logic and error handling")
    print("- Proper PropertyGroup system for Blender compatibility")
    print("- Robust import system with fallback mechanisms")
    print("- Better error handling throughout the codebase")

if __name__ == "__main__":
    main()
