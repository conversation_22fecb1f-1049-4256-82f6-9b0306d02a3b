"""
BlenderGPT Security Module
Provides comprehensive code validation and safety checks for AI-generated code
Enhanced with advanced security features and threat detection
"""

import ast
import re
import bpy
import sys
import os
import traceback
from typing import List, Tuple, Dict, Any, Set

class CodeSecurityValidator:
    """Enhanced security validator for AI-generated code with comprehensive threat detection"""

    # Dangerous functions/modules that should be blocked
    DANGEROUS_IMPORTS = {
        'os', 'sys', 'subprocess', 'shutil', 'pathlib', 'glob',
        'socket', 'urllib', 'requests', 'http', 'ftplib',
        'smtplib', 'poplib', 'imaplib', 'telnetlib',
        'pickle', 'marshal', 'shelve', 'dbm', 'sqlite3',
        '__import__', 'eval', 'exec', 'compile',
        'ctypes', 'multiprocessing', 'threading',
        'importlib', 'pkgutil', 'zipimport',
        'tempfile', 'webbrowser', 'platform'
    }

    DANGEROUS_FUNCTIONS = {
        'exec', 'eval', 'compile', '__import__',
        'open', 'file', 'input', 'raw_input',
        'exit', 'quit', 'reload', 'delattr', 'setattr',
        'getattr', 'hasattr', 'vars', 'dir',
        'globals', 'locals', 'memoryview'
    }

    DANGEROUS_ATTRIBUTES = {
        '__class__', '__bases__', '__subclasses__',
        '__globals__', '__locals__', '__dict__',
        '__code__', '__func__', '__closure__',
        '__defaults__', '__kwdefaults__', '__annotations__',
        '__module__', '__qualname__', '__doc__'
    }

    # Suspicious patterns that might indicate malicious intent
    SUSPICIOUS_PATTERNS = {
        r'\.format\s*\(',  # String formatting can be dangerous
        r'%\s*\(',         # Old-style string formatting
        r'exec\s*\(',      # Dynamic code execution
        r'eval\s*\(',      # Expression evaluation
        r'__.*__',         # Dunder methods/attributes
        r'setattr\s*\(',   # Dynamic attribute setting
        r'getattr\s*\(',   # Dynamic attribute getting
        r'delattr\s*\(',   # Dynamic attribute deletion
        r'import\s+\w+\s+as\s+\w+',  # Import aliasing (can hide dangerous imports)
    }

    # Allowed Blender modules (expanded and more specific)
    ALLOWED_IMPORTS = {
        'bpy', 'bmesh', 'mathutils', 'bgl', 'blf',
        'gpu', 'gpu_extras', 'bpy_extras', 'addon_utils',
        'math', 'random', 'time', 'datetime',
        'json', 're', 'collections', 'itertools',
        'operator', 'functools', 'copy', 'enum',
        'typing', 'dataclasses', 'abc'
    }

    # Maximum allowed code length (prevent code bombs)
    MAX_CODE_LENGTH = 10000

    # Maximum allowed nesting depth
    MAX_NESTING_DEPTH = 10

    def __init__(self):
        self.warnings = []
        self.errors = []
        self.nesting_depth = 0

    def validate_code(self, code: str) -> Tuple[bool, List[str], List[str]]:
        """
        Enhanced code validation with comprehensive security checks
        Returns: (is_safe, warnings, errors)
        """
        self.warnings.clear()
        self.errors.clear()
        self.nesting_depth = 0

        try:
            # Basic length check
            if len(code) > self.MAX_CODE_LENGTH:
                self.errors.append(f"Code too long ({len(code)} chars). Maximum allowed: {self.MAX_CODE_LENGTH}")
                return False, self.warnings.copy(), self.errors.copy()

            # Check for empty or whitespace-only code
            if not code.strip():
                self.errors.append("Empty or whitespace-only code detected")
                return False, self.warnings.copy(), self.errors.copy()

            # Parse the code into AST
            tree = ast.parse(code)

            # Comprehensive security checks
            self._check_imports(tree)
            self._check_function_calls(tree)
            self._check_attribute_access(tree)
            self._check_file_operations(tree)
            self._check_nesting_depth(tree)
            self._check_suspicious_constructs(tree)

            # Pattern-based checks
            self._check_string_patterns(code)
            self._check_suspicious_patterns(code)

            # Final safety assessment
            is_safe = len(self.errors) == 0

            # Add summary information
            if self.warnings:
                self.warnings.append(f"Total warnings: {len(self.warnings) - 1}")
            if self.errors:
                self.errors.append(f"Total errors: {len(self.errors) - 1}")

            return is_safe, self.warnings.copy(), self.errors.copy()

        except SyntaxError as e:
            self.errors.append(f"Syntax error in generated code: {e}")
            return False, self.warnings.copy(), self.errors.copy()
        except RecursionError:
            self.errors.append("Code structure too complex (recursion limit exceeded)")
            return False, self.warnings.copy(), self.errors.copy()
        except Exception as e:
            self.errors.append(f"Code validation error: {e}")
            self.errors.append(f"Traceback: {traceback.format_exc()}")
            return False, self.warnings.copy(), self.errors.copy()
    
    def _check_imports(self, tree: ast.AST):
        """Enhanced import checking with detailed analysis"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    module_name = alias.name.split('.')[0]
                    if module_name in self.DANGEROUS_IMPORTS:
                        self.errors.append(f"Dangerous import blocked: {alias.name}")
                    elif module_name not in self.ALLOWED_IMPORTS:
                        self.warnings.append(f"Unusual import detected: {alias.name} - verify necessity")

                    # Check for import aliasing that might hide dangerous modules
                    if alias.asname and alias.asname != alias.name:
                        self.warnings.append(f"Import aliasing detected: {alias.name} as {alias.asname}")

            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    module_name = node.module.split('.')[0]
                    if module_name in self.DANGEROUS_IMPORTS:
                        self.errors.append(f"Dangerous from-import blocked: from {node.module}")
                    elif module_name not in self.ALLOWED_IMPORTS:
                        self.warnings.append(f"Unusual from-import: from {node.module}")

                    # Check for wildcard imports
                    for alias in node.names:
                        if alias.name == '*':
                            self.warnings.append(f"Wildcard import detected: from {node.module} import *")

    def _check_function_calls(self, tree: ast.AST):
        """Enhanced function call checking"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                if func_name in self.DANGEROUS_FUNCTIONS:
                    self.errors.append(f"Dangerous function call blocked: {func_name}()")

                # Check for dynamic function calls
                if isinstance(node.func, ast.Attribute):
                    if hasattr(node.func, 'value') and isinstance(node.func.value, ast.Name):
                        if node.func.value.id in ['globals', 'locals', 'vars']:
                            self.errors.append(f"Dynamic function call detected: {func_name}()")

    def _check_attribute_access(self, tree: ast.AST):
        """Enhanced attribute access checking"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Attribute):
                if node.attr in self.DANGEROUS_ATTRIBUTES:
                    self.errors.append(f"Dangerous attribute access blocked: .{node.attr}")

                # Check for chained dangerous attribute access
                if hasattr(node, 'value') and isinstance(node.value, ast.Attribute):
                    if node.value.attr in self.DANGEROUS_ATTRIBUTES:
                        self.errors.append(f"Chained dangerous attribute access: .{node.value.attr}.{node.attr}")

    def _check_file_operations(self, tree: ast.AST):
        """Enhanced file operation checking"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                if func_name in ['open', 'file']:
                    self.warnings.append("File operation detected - verify file paths and permissions")
                elif func_name in ['write', 'read', 'readline', 'readlines']:
                    self.warnings.append(f"File I/O operation detected: {func_name}()")
    
    def _check_nesting_depth(self, tree: ast.AST):
        """Check for excessive nesting depth"""
        def check_depth(node, current_depth=0):
            if current_depth > self.MAX_NESTING_DEPTH:
                self.warnings.append(f"Excessive nesting depth detected: {current_depth} levels")
                return

            for child in ast.iter_child_nodes(node):
                if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try, ast.FunctionDef, ast.ClassDef)):
                    check_depth(child, current_depth + 1)
                else:
                    check_depth(child, current_depth)

        check_depth(tree)

    def _check_suspicious_constructs(self, tree: ast.AST):
        """Check for suspicious code constructs"""
        for node in ast.walk(tree):
            # Check for lambda functions (can be used to hide malicious code)
            if isinstance(node, ast.Lambda):
                self.warnings.append("Lambda function detected - verify functionality")

            # Check for list/dict comprehensions with complex expressions
            elif isinstance(node, (ast.ListComp, ast.DictComp, ast.SetComp, ast.GeneratorExp)):
                if len(list(ast.walk(node))) > 10:  # Arbitrary complexity threshold
                    self.warnings.append("Complex comprehension detected - verify logic")

            # Check for try/except blocks that might hide errors
            elif isinstance(node, ast.Try):
                if not node.handlers:
                    self.warnings.append("Try block without exception handlers detected")
                else:
                    for handler in node.handlers:
                        if handler.type is None:  # bare except:
                            self.warnings.append("Bare except clause detected - may hide errors")

    def _check_string_patterns(self, code: str):
        """Enhanced string pattern checking"""
        dangerous_patterns = [
            (r'rm\s+-rf', "Dangerous shell command pattern"),
            (r'del\s+/[sq]', "Dangerous deletion pattern"),
            (r'format\s*\(', "String formatting (potential injection)"),
            (r'\.format\s*\(', "String formatting method (potential injection)"),
            (r'%\s*\(', "Old-style string formatting (potential injection)"),
            (r'subprocess\.', "Subprocess usage detected"),
            (r'os\.system', "OS system call detected"),
            (r'eval\s*\(', "Dynamic code evaluation"),
            (r'exec\s*\(', "Dynamic code execution"),
        ]

        for pattern, description in dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                self.warnings.append(f"{description}: {pattern}")

    def _check_suspicious_patterns(self, code: str):
        """Check for additional suspicious patterns"""
        for pattern in self.SUSPICIOUS_PATTERNS:
            if re.search(pattern, code, re.IGNORECASE):
                self.warnings.append(f"Suspicious pattern detected: {pattern}")

        # Check for encoded strings that might hide malicious content
        if re.search(r'base64|decode|encode|hex|unhex', code, re.IGNORECASE):
            self.warnings.append("Encoding/decoding operations detected - verify content")

        # Check for network-related patterns
        if re.search(r'http|ftp|socket|urllib|requests', code, re.IGNORECASE):
            self.warnings.append("Network-related operations detected")

    def _get_function_name(self, func_node) -> str:
        """Extract function name from AST node with enhanced detection"""
        if isinstance(func_node, ast.Name):
            return func_node.id
        elif isinstance(func_node, ast.Attribute):
            return func_node.attr
        elif isinstance(func_node, ast.Call):
            # Handle nested function calls
            return self._get_function_name(func_node.func)
        return ""

class CodePreviewDialog:
    """Dialog for previewing and approving AI-generated code"""
    
    @staticmethod
    def show_preview(code: str, warnings: List[str], errors: List[str]) -> bool:
        """
        Show code preview dialog
        Returns True if user approves execution
        """
        # This will be implemented as a Blender modal dialog
        # For now, we'll use console output
        print("\n" + "="*60)
        print("AI GENERATED CODE PREVIEW")
        print("="*60)
        print(code)
        print("="*60)
        
        if errors:
            print("SECURITY ERRORS:")
            for error in errors:
                print(f"  ❌ {error}")
            print("CODE EXECUTION BLOCKED!")
            return False
            
        if warnings:
            print("WARNINGS:")
            for warning in warnings:
                print(f"  ⚠️  {warning}")
        
        print("="*60)
        # In real implementation, this would be a modal dialog
        return True  # For now, always approve if no errors

def validate_and_preview_code(code: str) -> Tuple[bool, str]:
    """
    Main function to validate and preview code
    Returns: (approved, sanitized_code)
    """
    validator = CodeSecurityValidator()
    is_safe, warnings, errors = validator.validate_code(code)
    
    if not is_safe:
        # Show preview dialog
        approved = CodePreviewDialog.show_preview(code, warnings, errors)
        if not approved:
            return False, ""
    
    # If we get here, code is approved
    return True, code

def create_safe_execution_environment():
    """Create a comprehensive restricted execution environment"""

    # Safe built-in functions
    safe_builtins = {
        # Type constructors
        'str': str, 'int': int, 'float': float, 'bool': bool, 'complex': complex,
        'list': list, 'dict': dict, 'tuple': tuple, 'set': set, 'frozenset': frozenset,

        # Iteration and sequence functions
        'len': len, 'range': range, 'enumerate': enumerate,
        'zip': zip, 'map': map, 'filter': filter,
        'iter': iter, 'next': next, 'slice': slice,

        # Math functions
        'min': min, 'max': max, 'sum': sum, 'abs': abs,
        'round': round, 'pow': pow, 'divmod': divmod,

        # Sorting and ordering
        'sorted': sorted, 'reversed': reversed,

        # Type checking
        'isinstance': isinstance, 'issubclass': issubclass,
        'type': type, 'callable': callable,

        # Utility functions
        'print': print,  # Allow print for debugging
        'repr': repr, 'str': str, 'hash': hash,
        'id': id, 'ord': ord, 'chr': chr,
        'bin': bin, 'oct': oct, 'hex': hex,

        # Exception types (for proper error handling)
        'Exception': Exception, 'ValueError': ValueError,
        'TypeError': TypeError, 'IndexError': IndexError,
        'KeyError': KeyError, 'AttributeError': AttributeError,
    }

    # Safe modules with restricted access
    safe_modules = {}

    try:
        # Import bpy safely
        import bpy
        safe_modules['bpy'] = bpy
    except ImportError:
        print("Warning: bpy module not available in execution environment")

    try:
        # Import safe standard library modules
        import math
        import random
        import time
        import json
        import re
        import collections
        import itertools

        safe_modules.update({
            'math': math,
            'random': random,
            'time': time,
            'json': json,
            're': re,
            'collections': collections,
            'itertools': itertools,
        })
    except ImportError as e:
        print(f"Warning: Some standard modules not available: {e}")

    # Create the safe globals dictionary
    safe_globals = {
        '__builtins__': safe_builtins,
        '__name__': '__main__',
        '__doc__': None,
    }

    # Add safe modules
    safe_globals.update(safe_modules)

    return safe_globals

def validate_execution_result(result: Any) -> Tuple[bool, str]:
    """Validate the result of code execution for additional safety"""
    try:
        # Check if result contains any dangerous objects
        if hasattr(result, '__dict__'):
            for attr_name in dir(result):
                if attr_name.startswith('__') and attr_name.endswith('__'):
                    if attr_name in CodeSecurityValidator.DANGEROUS_ATTRIBUTES:
                        return False, f"Execution result contains dangerous attribute: {attr_name}"

        return True, "Execution result validated successfully"
    except Exception as e:
        return False, f"Error validating execution result: {e}"
