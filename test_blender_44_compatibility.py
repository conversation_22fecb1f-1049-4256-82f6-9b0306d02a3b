"""
Test script for Blender 4.4 compatibility
Tests icon names, property registration, and UI elements
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_icon_compatibility():
    """Test if all used icons are valid in Blender 4.4"""
    print("🎨 Testing Icon Compatibility...")
    
    # Icons used in BlenderGPT
    used_icons = [
        "TOOL_SETTINGS", "FILE_REFRESH", "LOCKED", "TEXT", "INFO", 
        "NONE", "SCRIPT", "TRASH", "PREFERENCES", "OPTIONS", 
        "KEYINGSET", "URL"
    ]
    
    # Valid icons from Blender 4.4 (subset for testing)
    valid_icons_44 = {
        'NONE', 'BLANK1', 'AUTOMERGE_OFF', 'AUTOMERGE_ON', 'CHECKBOX_DEHLT', 
        'CHECKBOX_HLT', 'CLIPUV_DEHLT', 'CLIPUV_HLT', 'DECORATE_UNLOCKED', 
        'DECORATE_LOCKED', 'FAKE_USER_OFF', 'FAKE_USER_ON', 'HIDE_ON', 'HIDE_OFF',
        'CANCEL', 'ERROR', 'QUESTION', 'ADD', 'ARROW_LEFTRIGHT', 'AUTO', 'BLENDER',
        'CHECKMARK', 'COLLAPSEMENU', 'COLLECTION_NEW', 'COLOR', 'COPY_ID',
        'DISCLOSURE_TRI_DOWN', 'DISCLOSURE_TRI_RIGHT', 'DOT', 'DUPLICATE',
        'EYEDROPPER', 'FILE_NEW', 'FILE_TICK', 'FREEZE', 'FULLSCREEN_ENTER',
        'FULLSCREEN_EXIT', 'GHOST_DISABLED', 'GHOST_ENABLED', 'GRIP', 'HAND',
        'HELP', 'LINKED', 'MENU_PANEL', 'NODE_SEL', 'NODE', 'OBJECT_HIDDEN',
        'OPTIONS', 'PANEL_CLOSE', 'PLUGIN', 'PLUS', 'PRESET_NEW', 'QUIT',
        'RECOVER_LAST', 'REMOVE', 'RIGHTARROW_THIN', 'SCREEN_BACK', 'STATUSBAR',
        'STYLUS_PRESSURE', 'THREE_DOTS', 'TOPBAR', 'TRASH', 'TRIA_DOWN',
        'TRIA_LEFT', 'TRIA_RIGHT', 'TRIA_UP', 'UNLINKED', 'URL', 'VIEWZOOM',
        'WINDOW', 'WORKSPACE', 'X', 'ZOOM_ALL', 'ZOOM_IN', 'ZOOM_OUT',
        'ZOOM_PREVIOUS', 'ZOOM_SELECTED', 'MODIFIER', 'PARTICLES', 'PHYSICS',
        'SHADERFX', 'SPEAKER', 'OUTPUT', 'SCENE', 'TOOL_SETTINGS', 'LIGHT',
        'MATERIAL', 'TEXTURE', 'WORLD', 'ANIM', 'SCRIPT', 'GEOMETRY_NODES',
        'TEXT', 'ACTION', 'ASSET_MANAGER', 'CONSOLE', 'FILEBROWSER',
        'GEOMETRY_SET', 'GRAPH', 'IMAGE', 'INFO', 'NLA', 'NODE_COMPOSITING',
        'NODE_MATERIAL', 'NODE_TEXTURE', 'NODETREE', 'OUTLINER', 'PREFERENCES',
        'PROPERTIES', 'SEQUENCE', 'SOUND', 'SPREADSHEET', 'TIME', 'TRACKER',
        'UV', 'VIEW3D', 'KEYINGSET', 'LOCKED', 'FILE_REFRESH'
    }
    
    invalid_icons = []
    valid_icons = []
    
    for icon in used_icons:
        if icon in valid_icons_44:
            valid_icons.append(icon)
            print(f"  ✅ {icon} - Valid")
        else:
            invalid_icons.append(icon)
            print(f"  ❌ {icon} - Invalid")
    
    print(f"\n📊 Icon Test Results:")
    print(f"  Valid icons: {len(valid_icons)}/{len(used_icons)}")
    print(f"  Invalid icons: {len(invalid_icons)}")
    
    if invalid_icons:
        print(f"  ⚠️ Invalid icons found: {invalid_icons}")
        return False
    else:
        print(f"  ✅ All icons are valid for Blender 4.4!")
        return True

def test_property_registration():
    """Test property registration system"""
    print("\n📋 Testing Property Registration...")
    
    try:
        from properties import GPT4_ChatMessage, register_properties, unregister_properties
        print("  ✅ Properties module imported successfully")
        
        # Test PropertyGroup class
        if hasattr(GPT4_ChatMessage, '__annotations__'):
            annotations = GPT4_ChatMessage.__annotations__
            print(f"  ✅ GPT4_ChatMessage has {len(annotations)} properties")
            
            expected_props = ['type', 'content', 'timestamp', 'approved']
            missing_props = [prop for prop in expected_props if prop not in annotations]
            
            if missing_props:
                print(f"  ❌ Missing properties: {missing_props}")
                return False
            else:
                print(f"  ✅ All expected properties found")
        
        print("  ✅ Property registration system is compatible")
        return True
        
    except ImportError as e:
        print(f"  ❌ Failed to import properties module: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Property registration test failed: {e}")
        return False

def test_import_system():
    """Test the enhanced import system"""
    print("\n🔧 Testing Import System...")
    
    try:
        # Test utilities import
        from utilities import safe_import_modules
        print("  ✅ Utilities module imported successfully")
        
        # Test security import
        from security import CodeSecurityValidator, create_safe_execution_environment
        print("  ✅ Security module imported successfully")
        
        # Test API providers import
        from api_providers import APIProviderManager, APIProvider
        print("  ✅ API providers module imported successfully")
        
        print("  ✅ All modules imported successfully")
        return True
        
    except ImportError as e:
        print(f"  ❌ Import test failed: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error in import test: {e}")
        return False

def test_blender_44_features():
    """Test Blender 4.4 specific features and compatibility"""
    print("\n🚀 Testing Blender 4.4 Features...")
    
    # Test enum property format
    enum_items = [
        ("openai", "OpenAI", "Use OpenAI API"),
        ("anthropic", "Anthropic Claude", "Use Anthropic Claude API"),
        ("ollama", "Ollama (Local)", "Use local Ollama API"),
        ("custom", "Custom API", "Use custom OpenAI-compatible API"),
    ]
    
    # Validate enum format
    for item in enum_items:
        if not isinstance(item, tuple) or len(item) != 3:
            print(f"  ❌ Invalid enum item format: {item}")
            return False
        if not all(isinstance(x, str) for x in item):
            print(f"  ❌ Enum item contains non-string values: {item}")
            return False
    
    print("  ✅ Enum properties format is valid")
    
    # Test string property format
    string_props = [
        ("gpt4_model", "Model to use", "gpt-4"),
        ("gpt4_custom_base_url", "Base URL for custom API", "http://localhost:8000/v1"),
        ("gpt4_chat_input", "Enter your message", ""),
    ]
    
    for prop_name, description, default in string_props:
        if not isinstance(prop_name, str) or not prop_name:
            print(f"  ❌ Invalid property name: {prop_name}")
            return False
        if not isinstance(description, str):
            print(f"  ❌ Invalid description for {prop_name}: {description}")
            return False
        if not isinstance(default, str):
            print(f"  ❌ Invalid default value for {prop_name}: {default}")
            return False
    
    print("  ✅ String properties format is valid")
    print("  ✅ Blender 4.4 compatibility tests passed")
    return True

def main():
    """Run all Blender 4.4 compatibility tests"""
    print("🔍 BlenderGPT Blender 4.4 Compatibility Test Suite")
    print("=" * 60)
    
    tests = [
        ("Icon Compatibility", test_icon_compatibility),
        ("Property Registration", test_property_registration),
        ("Import System", test_import_system),
        ("Blender 4.4 Features", test_blender_44_features),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All compatibility tests passed! BlenderGPT is ready for Blender 4.4")
    else:
        print(f"⚠️ {total - passed} test(s) failed. Please review the issues above.")
    
    print("\n💡 Summary of Blender 4.4 Compatibility:")
    print("- ✅ Updated icon names to match Blender 4.4")
    print("- ✅ Enhanced PropertyGroup system")
    print("- ✅ Improved import mechanisms")
    print("- ✅ Better error handling and fallbacks")
    print("- ✅ Modern Python API usage")

if __name__ == "__main__":
    main()
