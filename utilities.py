import bpy
import re
import os
import sys
import json
import threading
import time
import traceback
from typing import Dict, List, Optional, Tuple, Any

# Improved import system with better error handling
def safe_import_modules():
    """Safely import required modules with fallback mechanisms"""
    try:
        # Try relative imports first (when running as addon)
        from .api_providers import APIProviderManager, APIProvider
        from .security import validate_and_preview_code, create_safe_execution_environment
        return APIProviderManager, APIProvider, validate_and_preview_code, create_safe_execution_environment
    except ImportError:
        try:
            # Fallback for development/testing - absolute imports
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)

            from api_providers import APIProviderManager, APIProvider
            from security import validate_and_preview_code, create_safe_execution_environment
            return APIProviderManager, APIProvider, validate_and_preview_code, create_safe_execution_environment
        except ImportError as e:
            print(f"Warning: Could not import required modules: {e}")
            print(f"Traceback: {traceback.format_exc()}")

            # Create safe dummy classes for basic functionality
            class DummyAPIProviderManager:
                def create_provider(self, *args, **kwargs):
                    raise NotImplementedError("API providers not available. Please check module installation.")

            class DummyAPIProvider:
                OPENAI = "openai"
                ANTHROPIC = "anthropic"
                OLLAMA = "ollama"
                CUSTOM = "custom"

            def dummy_validate_and_preview_code(code):
                print("Warning: Security validation disabled - modules not available")
                return True, code

            def dummy_create_safe_execution_environment():
                # Return a minimal safe environment
                safe_builtins = {
                    'len': len, 'range': range, 'enumerate': enumerate,
                    'str': str, 'int': int, 'float': float, 'bool': bool,
                    'list': list, 'dict': dict, 'tuple': tuple, 'set': set,
                    'print': print
                }
                return {'__builtins__': safe_builtins, 'bpy': bpy}

            return DummyAPIProviderManager, DummyAPIProvider, dummy_validate_and_preview_code, dummy_create_safe_execution_environment

# Initialize modules
APIProviderManager, APIProvider, validate_and_preview_code, create_safe_execution_environment = safe_import_modules()


def get_api_key(context, addon_name):
    """Get API key based on selected provider"""
    preferences = context.preferences
    addon_prefs = preferences.addons[addon_name].preferences
    provider = context.scene.gpt4_api_provider

    if provider == "openai" or provider == "custom":
        return addon_prefs.api_key
    elif provider == "anthropic":
        return getattr(addon_prefs, "anthropic_api_key", "")
    elif provider == "openrouter":
        return getattr(addon_prefs, "openrouter_api_key", "")
    else:
        return addon_prefs.api_key


def init_props():
    """Initialize Blender properties for the addon"""
    try:
        # Import and register properties from separate module
        from .properties import register_properties
        register_properties()
        print("BlenderGPT: Properties registered successfully")
    except ImportError:
        try:
            # Fallback for development
            from properties import register_properties
            register_properties()
            print("BlenderGPT: Properties registered successfully (fallback)")
        except ImportError as e:
            print(f"BlenderGPT: Failed to register properties: {e}")
            # Create minimal fallback properties
            _create_fallback_properties()

def clear_props():
    """Clear Blender properties when addon is disabled"""
    try:
        # Import and unregister properties from separate module
        from .properties import unregister_properties
        unregister_properties()
        print("BlenderGPT: Properties unregistered successfully")
    except ImportError:
        try:
            # Fallback for development
            from properties import unregister_properties
            unregister_properties()
            print("BlenderGPT: Properties unregistered successfully (fallback)")
        except ImportError:
            # Manual cleanup as fallback
            _clear_fallback_properties()

def _create_fallback_properties():
    """Create minimal fallback properties if main registration fails"""
    try:
        bpy.types.Scene.gpt4_api_provider = bpy.props.EnumProperty(
            items=[("openai", "OpenAI", "")],
            default="openai"
        )
        bpy.types.Scene.gpt4_model = bpy.props.StringProperty(default="gpt-4")
        bpy.types.Scene.gpt4_chat_input = bpy.props.StringProperty(default="")
        bpy.types.Scene.gpt4_button_pressed = bpy.props.BoolProperty(default=False)
        bpy.types.Scene.gpt4_security_enabled = bpy.props.BoolProperty(default=True)
        bpy.types.Scene.gpt4_code_preview_enabled = bpy.props.BoolProperty(default=True)
        bpy.types.Scene.gpt4_custom_base_url = bpy.props.StringProperty(default="http://localhost:8000/v1")
        print("BlenderGPT: Fallback properties created")
    except Exception as e:
        print(f"BlenderGPT: Failed to create fallback properties: {e}")

def _clear_fallback_properties():
    """Clear fallback properties manually"""
    try:
        properties_to_clear = [
            'gpt4_api_provider', 'gpt4_model', 'gpt4_chat_input',
            'gpt4_button_pressed', 'gpt4_security_enabled',
            'gpt4_code_preview_enabled', 'gpt4_custom_base_url'
        ]
        for prop in properties_to_clear:
            if hasattr(bpy.types.Scene, prop):
                delattr(bpy.types.Scene, prop)
        print("BlenderGPT: Fallback properties cleared")
    except Exception as e:
        print(f"BlenderGPT: Failed to clear fallback properties: {e}")

def generate_blender_code(prompt: str, chat_history, context, system_prompt: str) -> Optional[str]:
    """
    Generate Blender code using the selected API provider with enhanced error handling
    """
    try:
        # Validate inputs
        if not prompt or not prompt.strip():
            raise ValueError("Empty prompt provided")

        if not context:
            raise ValueError("No context provided")

        # Get API provider manager
        try:
            provider_manager = APIProviderManager()
        except Exception as e:
            raise RuntimeError(f"Failed to initialize API provider manager: {e}")

        # Get provider settings from context with validation
        try:
            provider_type = APIProvider(context.scene.gpt4_api_provider)
        except (ValueError, AttributeError) as e:
            raise ValueError(f"Invalid API provider selected: {e}")

        try:
            api_key = get_api_key(context, __name__)
        except Exception as e:
            print(f"Warning: Could not retrieve API key: {e}")
            api_key = ""

        # Create provider instance with comprehensive error handling
        provider = None
        try:
            if provider_type == APIProvider.OPENAI:
                if not api_key:
                    raise ValueError("OpenAI API key is required")
                provider = provider_manager.create_provider(
                    provider_type,
                    api_key=api_key,
                    model=getattr(context.scene, 'gpt4_model', 'gpt-4')
                )
            elif provider_type == APIProvider.ANTHROPIC:
                if not api_key:
                    raise ValueError("Anthropic API key is required")
                provider = provider_manager.create_provider(
                    provider_type,
                    api_key=api_key,
                    model=getattr(context.scene, 'gpt4_model', 'claude-3-sonnet-20240229')
                )
            elif provider_type == APIProvider.OLLAMA:
                base_url = getattr(context.scene, 'gpt4_custom_base_url', 'http://localhost:11434')
                provider = provider_manager.create_provider(
                    provider_type,
                    base_url=base_url or "http://localhost:11434",
                    model=getattr(context.scene, 'gpt4_model', 'llama2')
                )
            elif provider_type == APIProvider.OPENROUTER:
                if not api_key:
                    raise ValueError("OpenRouter API key is required")
                provider = provider_manager.create_provider(
                    provider_type,
                    api_key=api_key,
                    model=getattr(context.scene, 'gpt4_model', 'openai/gpt-3.5-turbo')
                )
            elif provider_type == APIProvider.CUSTOM:
                if not api_key:
                    raise ValueError("Custom API key is required")
                base_url = getattr(context.scene, 'gpt4_custom_base_url', '')
                if not base_url:
                    raise ValueError("Custom API base URL is required")
                provider = provider_manager.create_provider(
                    provider_type,
                    api_key=api_key,
                    base_url=base_url,
                    model=getattr(context.scene, 'gpt4_model', 'gpt-3.5-turbo')
                )
            else:
                raise ValueError(f"Unsupported provider: {provider_type}")

        except Exception as e:
            raise RuntimeError(f"Failed to create API provider: {e}")

        # Prepare messages with validation
        try:
            messages = [{"role": "system", "content": system_prompt}]

            # Add chat history (last 10 messages) with validation
            if chat_history:
                for message in chat_history[-10:]:
                    try:
                        if hasattr(message, 'type') and hasattr(message, 'content'):
                            if message.type == "assistant":
                                messages.append({"role": "assistant", "content": str(message.content)})
                            else:
                                messages.append({"role": str(message.type).lower(), "content": str(message.content)})
                    except Exception as e:
                        print(f"Warning: Skipping invalid message in chat history: {e}")

            # Add the current user message
            enhanced_prompt = f"Can you please write Blender code for me that accomplishes the following task: {prompt}? \n. Do not respond with anything that is not Python code. Do not provide explanations"
            messages.append({"role": "user", "content": enhanced_prompt})

        except Exception as e:
            raise RuntimeError(f"Failed to prepare messages: {e}")

        # Generate code with comprehensive error handling
        completion_text = ''
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Try streaming first
                try:
                    for event in provider.create_chat_completion_stream(messages, max_tokens=1500):
                        if 'choices' in event and len(event['choices']) > 0:
                            delta = event['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content = delta['content']
                                completion_text += content
                                print(completion_text, flush=True, end='\r')
                    break  # Success, exit retry loop

                except Exception as stream_error:
                    print(f"Streaming failed (attempt {retry_count + 1}), falling back to non-streaming: {stream_error}")
                    # Fallback to non-streaming
                    response = provider.create_chat_completion(messages, max_tokens=1500)
                    if 'choices' in response and len(response['choices']) > 0:
                        completion_text = response['choices'][0]['message']['content']
                        break  # Success, exit retry loop
                    else:
                        raise RuntimeError("No valid response from API")

            except Exception as api_error:
                retry_count += 1
                if retry_count >= max_retries:
                    raise RuntimeError(f"API call failed after {max_retries} attempts: {api_error}")
                print(f"API call failed (attempt {retry_count}), retrying: {api_error}")
                time.sleep(1)  # Wait before retry

        # Validate and process the response
        if not completion_text or not completion_text.strip():
            raise RuntimeError("Empty response from API")

        # Extract code from markdown blocks with improved regex
        try:
            code_blocks = re.findall(r'```(?:python)?\s*(.*?)```', completion_text, re.DOTALL)
            if code_blocks:
                completion_text = code_blocks[0].strip()
            else:
                # If no code blocks found, use the entire response
                completion_text = completion_text.strip()

            # Remove any remaining language identifiers
            completion_text = re.sub(r'^python\s*\n', '', completion_text, flags=re.MULTILINE)

            # Final validation
            if not completion_text:
                raise RuntimeError("No valid code extracted from response")

        except Exception as e:
            raise RuntimeError(f"Failed to process API response: {e}")

        return completion_text

    except ValueError as e:
        print(f"Input validation error: {e}")
        return None
    except RuntimeError as e:
        print(f"Runtime error generating code: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error generating code: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return None

def execute_code_safely(code: str, context) -> Tuple[bool, str]:
    """
    Execute code safely with comprehensive security validation and error handling
    Returns: (success, error_message)
    """
    try:
        # Input validation
        if not code or not code.strip():
            return False, "No code provided for execution"

        if not context:
            return False, "No context provided for execution"

        # Security validation if enabled
        try:
            if getattr(context.scene, 'gpt4_security_enabled', True):
                approved, validated_code = validate_and_preview_code(code)
                if not approved:
                    return False, "Code execution blocked by security validation"
                code = validated_code
        except Exception as e:
            print(f"Warning: Security validation failed: {e}")
            return False, f"Security validation error: {e}"

        # Create safe execution environment
        try:
            safe_globals = create_safe_execution_environment()
        except Exception as e:
            return False, f"Failed to create safe execution environment: {e}"

        # Pre-execution validation
        try:
            # Try to compile the code first to catch syntax errors
            compile(code, '<generated_code>', 'exec')
        except SyntaxError as e:
            return False, f"Syntax error in generated code: {e}"
        except Exception as e:
            return False, f"Code compilation error: {e}"

        # Execute the code with timeout protection
        try:
            # Store original stdout/stderr for restoration
            import sys
            original_stdout = sys.stdout
            original_stderr = sys.stderr

            # Capture output for monitoring
            from io import StringIO
            captured_output = StringIO()
            sys.stdout = captured_output
            sys.stderr = captured_output

            try:
                # Execute with timeout (using threading for simple timeout)
                import threading
                execution_result = {'success': False, 'error': None}

                def execute_with_timeout():
                    try:
                        exec(code, safe_globals)
                        execution_result['success'] = True
                    except Exception as e:
                        execution_result['error'] = e

                execution_thread = threading.Thread(target=execute_with_timeout)
                execution_thread.daemon = True
                execution_thread.start()
                execution_thread.join(timeout=30)  # 30 second timeout

                if execution_thread.is_alive():
                    return False, "Code execution timed out (30 seconds)"

                if not execution_result['success']:
                    if execution_result['error']:
                        raise execution_result['error']
                    else:
                        raise RuntimeError("Unknown execution error")

            finally:
                # Restore stdout/stderr
                sys.stdout = original_stdout
                sys.stderr = original_stderr

                # Get captured output for logging
                output = captured_output.getvalue()
                if output:
                    print(f"Code execution output: {output}")

        except MemoryError:
            return False, "Code execution failed: Out of memory"
        except RecursionError:
            return False, "Code execution failed: Maximum recursion depth exceeded"
        except KeyboardInterrupt:
            return False, "Code execution interrupted by user"
        except Exception as e:
            error_msg = f"Error executing generated code: {str(e)}"
            print(f"Execution error details: {traceback.format_exc()}")
            return False, error_msg

        # Post-execution validation
        try:
            # Validate that Blender is still in a consistent state
            if hasattr(bpy, 'context') and bpy.context:
                # Basic sanity checks
                if not hasattr(bpy.context, 'scene'):
                    return False, "Code execution corrupted Blender context"
        except Exception as e:
            print(f"Warning: Post-execution validation failed: {e}")

        return True, "Code executed successfully"

    except Exception as e:
        error_msg = f"Unexpected error in safe execution: {str(e)}"
        print(f"Safe execution error: {traceback.format_exc()}")
        return False, error_msg

def get_scene_context(context) -> Dict[str, Any]:
    """Get current scene context for better AI understanding"""
    scene_info = {
        "selected_objects": [obj.name for obj in context.selected_objects],
        "active_object": context.active_object.name if context.active_object else None,
        "scene_objects": [obj.name for obj in context.scene.objects],
        "current_mode": context.mode,
        "cursor_location": list(context.scene.cursor.location),
        "frame_current": context.scene.frame_current,
    }
    return scene_info

def enhance_prompt_with_context(prompt: str, context) -> str:
    """Enhance user prompt with scene context"""
    scene_info = get_scene_context(context)

    context_str = f"""
Current scene context:
- Selected objects: {', '.join(scene_info['selected_objects']) if scene_info['selected_objects'] else 'None'}
- Active object: {scene_info['active_object'] or 'None'}
- Current mode: {scene_info['current_mode']}
- Total objects in scene: {len(scene_info['scene_objects'])}

User request: {prompt}
"""
    return context_str

def split_area_to_text_editor(context):
    """Split current area to show text editor"""
    area = context.area
    for region in area.regions:
        if region.type == 'WINDOW':
            override = {'area': area, 'region': region}
            bpy.ops.screen.area_split(override, direction='VERTICAL', factor=0.5)
            break

    new_area = context.screen.areas[-1]
    new_area.type = 'TEXT_EDITOR'
    return new_area

def get_available_models(provider_type: str, api_key: str = "", base_url: str = "") -> List[str]:
    """Get available models for the selected provider"""
    try:
        provider_manager = APIProviderManager()
        provider_enum = APIProvider(provider_type)

        if provider_enum == APIProvider.OPENAI:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, model="gpt-4")
        elif provider_enum == APIProvider.ANTHROPIC:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, model="claude-3-sonnet-20240229")
        elif provider_enum == APIProvider.OLLAMA:
            provider = provider_manager.create_provider(provider_enum, base_url=base_url or "http://localhost:11434", model="llama2")
        elif provider_enum == APIProvider.OPENROUTER:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, model="openai/gpt-3.5-turbo")
        elif provider_enum == APIProvider.CUSTOM:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, base_url=base_url, model="gpt-3.5-turbo")
        else:
            return []

        return provider.get_available_models()
    except Exception as e:
        print(f"Error getting models: {e}")
        return []